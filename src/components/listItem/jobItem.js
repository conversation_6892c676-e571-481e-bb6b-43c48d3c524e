import React, { Component } from 'react';
import { Text, Touchable, View } from '../index';
import { desColor, jobStyle } from '../../themes';
import I18n from '../../i18n';
import ImageBackground from '../imageBackground';
import res, { getEmployerAvatarSource } from '../../res';
import Image from '../image';
import { Badge, Icon } from 'react-native-elements';
import util from '../../util';

export default class JobItem extends Component {
  renderItemBadge = (value, props = {}) => {
    if (!value) {
      return null;
    }
    return (
      <Badge
        containerStyle={jobStyle.tagItem}
        value={value}
        badgeStyle={jobStyle.tagItemBadgeStyle}
        textStyle={jobStyle.tagText}
        {...props}
      />
    );
  };

  renderLocationView = (item) => {
    if (item.locations?.[0]?.addressCodePlace.name) {
      let distanceText = '';
      // 后端返回的 item.locations[0].distance 单位是厘米
      const distance = parseFloat(parseFloat(item.locations[0].distance) / 100).toFixed(0);
      if (distance > 0 && distance < 1000) {
        if (distance < 100) {
          distanceText = '<100m';
        } else {
          distanceText = `${distance}m`;
        }
      }

      if (distance >= 1000 && distance <= 100000) {
        distanceText = `${util.approximateValue(parseFloat(distance) / 1000, 1)}km`;
      }

      if (distance > 100000 && distance <= 5000000) {
        distanceText = '>100km';
      }
      if (distance > 5000000) {
        distanceText = '>500km';
      }
      return (
        <View style={jobStyle.locationBox}>
          <View style={jobStyle.locationLeft}>
            <Icon name="place" iconStyle={{ marginRight: 8 }} size={16} color="#CCC" />
            <Text numberOfLines={1} style={{ width: '85%' }}>
              {item.locations[0].addressCodePlace.name || '-'}
            </Text>
          </View>
          <View style={jobStyle.locationRight}>
            {distance > 0 ? <Text>{distanceText}</Text> : null}
          </View>
        </View>
      );
    } else {
      return <View style={{ height: 18, backgroundColor: '#FFF' }} />;
    }
  };

  onPress = () => this.props.onPress?.(this.props.item);

  render() {
    const { item, withoutFeedback, container } = this.props;
    const isShow =
      item.employer &&
      ((item.employer.industrialId && item.employer.industrialId.label) ||
        (item.employer.scaleId && item.employer.scaleId.label));
    console.log('logo', item.employer?.logo);
    return (
      <Touchable onPress={this.onPress} withoutFeedback={withoutFeedback}>
        <View style={[jobStyle.listContainer, container]}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text numberOfLines={1} style={[jobStyle.jobTitle, { flexGrow: 20, flexShrink: 200 }]}>
              {item.title}
            </Text>
            <Text style={jobStyle.salaryLabel}>
              {item.salaryId && item.salaryId.label ? item.salaryId.label : ''}
            </Text>
          </View>
          <View style={jobStyle.tags}>
            {this.renderItemBadge(item.jobLevelId && item.jobLevelId.label)}
            {this.renderItemBadge(
              item.workyears > 0 && `${item.workyears}${I18n.t('page_job_text_year')}`
            )}
            {this.renderItemBadge(item.qualificationId && item.qualificationId.label)}
            {this.renderItemBadge(item.major, { textProps: { numberOfLines: 1 } })}
            {item.jobLangs && item.jobLangs.length > 0 ? (
              item.jobLangs.map((lang, i) =>
                this.renderItemBadge(`${lang.languageId.label}${lang.languageLevelId.label}`, {
                  key: `${i + 1}`,
                })
              )
            ) : (
              <Text style={{ display: 'none' }} />
            )}
            {this.renderItemBadge(item.sex && item.sex.label)}
            {this.renderItemBadge(item.ageTo > 0 && `${item.ageFrom}-${item.ageTo}`)}
            {this.renderItemBadge(item.marital && item.marital.label)}
          </View>
          <View style={jobStyle.locationsBoxStyle}>
            {(item.locations && item.locations.length > 0) || item.distance ? (
              <View style={jobStyle.locationsStyle}>
                {item.locations.map((l) => l.locationId.label).length > 0 ? (
                  <Text numberOfLines={1} style={{ color: desColor, fontSize: 12 }}>
                    {item.locations.map((l) => l.locationId.label).join(' | ')}
                  </Text>
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
                {/* {item.distance ? (
                  <Text
                    style={{ color: desColor, fontSize: 12, marginLeft: 10 }}
                  >{`${item.distance}m`}</Text>
                ) : null} */}
              </View>
            ) : (
              <Text style={{ display: 'none' }} />
            )}
          </View>
          <View style={jobStyle.companyPannel}>
            <ImageBackground
              imageStyle={{ borderRadius: 10 }}
              style={jobStyle.companyLogo}
              source={getEmployerAvatarSource(item.employer?.logo ? item.employer.logo : '')}
              defaultSource={res.defaultEmployerAvatar}
              resizeMode="contain"
            >
              {item.employer &&
              item.employer.employerQualificationType &&
              item.employer.employerQualificationType.value === 1 ? (
                <Image style={jobStyle.v2} source={res.verify} />
              ) : (
                <View />
              )}
            </ImageBackground>
            <View style={[jobStyle.companyDetail, { flexShrink: 1 }]}>
              <View>
                <Text
                  numberOfLines={1}
                  style={[jobStyle.companyName, { width: '98%' }]}
                  ellipsizeMode="middle"
                >
                  {item.employer ? item.employer.company : ''}{' '}
                  {item.employer &&
                  item.employer.qualificationStatus &&
                  item.employer.qualificationStatus.value === 1 ? (
                    <Image source={res.iconVerify} style={{ width: 9, height: 9 }} />
                  ) : null}
                </Text>
              </View>
              {isShow ? (
                <Text style={jobStyle.companySubcribe}>
                  {item.employer.industrialId ? item.employer.industrialId.label : ''}
                  {item.employer.scaleId &&
                  item.employer.industrialId &&
                  item.employer.industrialId.label &&
                  item.employer.scaleId.label
                    ? ' | '
                    : ''}
                  {item.employer && item.employer.scaleId
                    ? `${item.employer.scaleId.label}${I18n.t('page_job_text_person')}`
                    : ''}
                </Text>
              ) : (
                <Text style={{ display: 'none' }} />
              )}
            </View>
          </View>

          <View>{this.renderLocationView(item)}</View>
        </View>
      </Touchable>
    );
  }
}
