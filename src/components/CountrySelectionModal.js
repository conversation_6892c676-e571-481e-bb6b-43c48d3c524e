import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { Icon } from 'react-native-elements';
import I18n from '../i18n';
import res from '../res';
import Image from './image';
import { deviceWidth, deviceHeight, statusBarHeight, hasEar } from '../common';
import { titleColor, subTitleColor } from '../themes';

// 国家列表数据 - 与countrySelection.js保持一致
const countryList = [
  {
    id: 1,
    name: '柬埔寨',
    nameEn: 'Cambodia',
    nameKm: 'កម្ពុជា',
    nameVi: 'Campuchia',
    nameTh: 'กัมพูชา',
    nameKo: '캄보디아',
    flag: res.khFlag,
    code: '855',
  },
  {
    id: 2,
    name: '菲律宾',
    nameEn: 'Philippines',
    nameKm: 'ហ្វីលីពីន',
    nameVi: 'Philippines',
    nameTh: 'ฟิลิปปินส์',
    nameKo: '필리핀',
    flag: res.phFlag,
    code: '63',
  },
  {
    id: 3,
    name: '新加坡',
    nameEn: 'Singapore',
    nameKm: 'សិង្ហបុរី',
    nameVi: 'Singapore',
    nameTh: 'สิงคโปร์',
    nameKo: '싱가포르',
    flag: res.sgFlag,
    code: '65',
  },
  {
    id: 4,
    name: '马来西亚',
    nameEn: 'Malaysia',
    nameKm: 'ម៉ាឡេស៊ី',
    nameVi: 'Malaysia',
    nameTh: 'มาเลเซีย',
    nameKo: '말레이시아',
    flag: res.myFlag,
    code: '60',
  },
  {
    id: 5,
    name: '印度尼西亚',
    nameEn: 'Indonesia',
    nameKm: 'ឥណ្ឌូនេស៊ី',
    nameVi: 'Indonesia',
    nameTh: 'อินโดนีเซีย',
    nameKo: '인도네시아',
    flag: res.mcFlag,
    code: '62',
  },
  {
    id: 6,
    name: '中国香港',
    nameEn: 'Hong Kong',
    nameKm: 'ហុងកុង',
    nameVi: 'Hồng Kông',
    nameTh: 'ฮ่องกง',
    nameKo: '홍콩',
    flag: res.hkFlag,
    code: '852',
  },
];

export default class CountrySelectionModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedCountry: props.selectedCountry || null,
    };
  }

  // 获取国家名称（根据当前语言）
  getCountryName = (country) => {
    const locale = I18n.locale;
    switch (locale) {
      case 'zh':
        return country.name;
      case 'en':
        return country.nameEn;
      case 'km':
        return country.nameKm;
      case 'vi':
        return country.nameVi;
      case 'th':
        return country.nameTh;
      case 'ko':
        return country.nameKo;
      default:
        return country.nameEn;
    }
  };

  // 选择国家
  onSelectCountry = (country) => {
    this.setState({ selectedCountry: country });
    if (this.props.onSelect) {
      this.props.onSelect(country);
    }
    if (this.props.onClose) {
      this.props.onClose();
    }
  };

  // 关闭弹出框
  onClose = () => {
    if (this.props.onClose) {
      this.props.onClose();
    }
  };

  // 渲染国家项
  renderCountryItem = ({ item }) => {
    const { selectedCountry } = this.state;
    const isSelected = selectedCountry && selectedCountry.id === item.id;

    return (
      <TouchableOpacity
        style={[styles.countryItem, isSelected && styles.selectedCountryItem]}
        onPress={() => this.onSelectCountry(item)}
      >
        <Image source={item.flag} style={styles.countryFlag} />
        <Text style={[styles.countryName, isSelected && styles.selectedCountryName]}>
          {this.getCountryName(item)}
        </Text>
        {isSelected && (
          <Icon
            type="ionicon"
            name="ios-checkmark-circle"
            size={20}
            color="#4a90e2"
            style={styles.checkIcon}
          />
        )}
      </TouchableOpacity>
    );
  };

  render() {
    const { visible } = this.props;

    return (
      <Modal
        visible={visible}
        transparent
        animationType="slide"
        onRequestClose={this.onClose}
      >
        <View style={styles.overlay}>
          <View style={styles.modalContainer}>
            <SafeAreaView style={styles.safeArea}>
              {/* 头部 */}
              <View style={styles.header}>
                <Text style={styles.title}>{I18n.t('page_country_selection_title')}</Text>
                <TouchableOpacity style={styles.closeButton} onPress={this.onClose}>
                  <Icon type="ionicon" name="ios-close" size={28} color={titleColor} />
                </TouchableOpacity>
              </View>

              {/* 国家列表 */}
              <View style={styles.content}>
                <FlatList
                  data={countryList}
                  renderItem={this.renderCountryItem}
                  keyExtractor={(item) => item.id.toString()}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.listContainer}
                />
              </View>
            </SafeAreaView>
          </View>
        </View>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: deviceHeight * 0.8,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: titleColor,
  },
  closeButton: {
    padding: 5,
  },
  content: {
    flex: 1,
  },
  listContainer: {
    padding: 20,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  selectedCountryItem: {
    borderColor: '#4a90e2',
    backgroundColor: '#f0f8ff',
  },
  countryFlag: {
    width: 30,
    height: 20,
    marginRight: 15,
    resizeMode: 'cover',
  },
  countryName: {
    flex: 1,
    fontSize: 16,
    color: titleColor,
    fontWeight: '500',
  },
  selectedCountryName: {
    color: '#4a90e2',
    fontWeight: '600',
  },
  checkIcon: {
    marginLeft: 10,
  },
});
