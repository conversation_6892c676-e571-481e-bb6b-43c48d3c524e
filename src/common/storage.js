const URL_HASH = 'urlHash';
const HAS_ADV = 'hasAdv';
const THEME = 'theme';
const IS_ENTERPRISE = 'isEnterprise';
const FIRST_INSTALL = 'firstInstall';

/**
 * Storage管理
 * 本地缓存保存
 */
class Storage {
  setUrlHash(url) {
    return global.storage.save({ key: URL_HASH, data: url });
  }

  getUrlHash() {
    return global.storage.load({ key: URL_HASH }).catch(() => Promise.resolve(null));
  }

  deleteUrlHash() {
    return global.storage.remove({ key: URL_HASH });
  }

  setHasAdv(has) {
    return global.storage.save({ key: HAS_ADV, data: has });
  }

  getHasAdv() {
    return global.storage.load({ key: HAS_ADV }).catch(() => Promise.resolve(null));
  }

  deleteHasAdv() {
    return global.storage.remove({ key: HAS_ADV });
  }

  async setTheme(theme) {
    return global.storage.save({ key: THEME, data: theme });
  }

  async getTheme() {
    return global.storage.load({ key: THEME }).catch(() => Promise.resolve(null));
  }

  async setEnterprise(isEnterprise) {
    return global.storage.save({ key: IS_ENTERPRISE, data: isEnterprise });
  }

  async getEnterprise() {
    return global.storage.load({ key: IS_ENTERPRISE }).catch(() => Promise.resolve(null));
  }

  async setFirstInstall(isFirst) {
    return global.storage.save({ key: FIRST_INSTALL, data: isFirst });
  }

  async getFirstInstall() {
    return global.storage.load({ key: FIRST_INSTALL }).catch(() => Promise.resolve(true));
  }

  // async getIsNotRemindCreateWallet(userId) {
  //   return global.storage
  //     .load({ key: IS_NOT_REMIND_CREATE_WALLET, id: userId.toString() })
  //     .catch(() => Promise.resolve(null));
  // }

  // async deleteIsNotRemindCreateWallet(userId) {
  //   return global.storage.remove({ key: IS_NOT_REMIND_CREATE_WALLET, id: userId.toString() });
  // }

  async clear() {
    try {
      global.storage.remove({ key: URL_HASH });
      global.storage.remove({ key: HAS_ADV });
      global.storage.remove({ key: THEME });
      global.storage.remove({ key: IS_ENTERPRISE });
      global.storage.remove({ key: FIRST_INSTALL });
      return await Promise.resolve(true);
    } catch (e) {
      return await Promise.reject(e);
    }
  }
}

export default new Storage();
