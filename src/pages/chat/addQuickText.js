import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { inject, observer } from 'mobx-react';
import { Text, View, TextInput, TouchableOpacity, ScrollView, Keyboard } from 'react-native';
import { Header, Icon, CheckBox } from 'react-native-elements';
import { dynamicStyle, globalStyle, headerStyle, bgColor } from '../../themes';
import GoBack from '../../components/goback';
import I18n from '../../i18n';
import LoadingModal from '../../components/loadingModal';
import util from '../../util';

function getStyle() {
  return {
    container: {
      flex: 1,
      backgroundColor: '#fff',
    },
    editContent: {
      padding: 15,
    },
    inputText: {
      height: 300,
      fontSize: 16,
      color: '#333',
      borderWidth: 1,
      borderColor: bgColor,
      paddingLeft: 8,
    },
    editInputText: {
      height: 300,
    },
    editTextCount: {
      color: '#999',
      textAlign: 'right',
      marginTop: 10,
    },
    numberCount: {
      color: 'orange',
    },
    line: {
      height: 1,
      backgroundColor: '#ccc',
      marginTop: 15,
      marginBottom: 15,
    },
  };
}

@inject('dynamicStore', 'dynamicAction')
@observer
export default class AddQuickText extends Component {
  style = getStyle();
  constructor(props) {
    super(props);
    this.showLoading = false;
    this.item = props.navigation.state.params.item;
    this.state = {
      msgText: this.item?.content || '',
      disabledPublish: false,
      count: this.item?.content?.length || 0,
      postData: {},
    };
  }

  componentDidMount() {
    global.emitter.on('hideLoading', this.onHideLoading);
  }

  componentWillUnmount() {
    global.emitter.off('hideLoading', this.onHideLoading);
  }

  onHideLoading = () => {
    this.showLoading = false;
  };

  publish = async () => {
    try {
      Keyboard.dismiss();
      const temp = Object.assign({}, this.state.postData);
      temp.content = this.state.msgText;
      if (!temp.content && temp.images.length === 0) {
        this.toast.show(I18n.t('page_dynamic_content_not_be_null'));
        return;
      }
      this.showLoading = true;
      this.setState({ disabledPublish: true });
      const data = await this.props.dynamicAction.publishDynamic(temp);
      if (data && data.successful) {
        this.toast.show(data.message);
        if (this.props.navigation.state.params) {
          this.props.navigation.state.params.onSelect();
        }
        this.showLoading = false;
        this.setState({ disabledPublish: false });
        setTimeout(() => {
          this.props.navigation.goBack();
        }, 1000);
      } else {
        this.showLoading = false;
        this.setState({ disabledPublish: false });
        this.toast.show(data && data.message);
      }
    } catch (error) {
      console.log('出错了', error);
      this.showLoading = false;
      this.setState({ disabledPublish: false });
    }
  };

  render() {
    const { style } = this;
    const { navigation } = this.props;
    const { disabledPublish, msgText } = this.state;
    return (
      <View style={style.container}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={{
            text: '添加快捷文本',
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            <TouchableOpacity
              disabled={disabledPublish}
              onPress={() =>
                util.HandlerOnceTap(() => {
                  this.publish();
                })
              }
            >
              <Text style={[headerStyle.rightBtn, { color: disabledPublish ? bgColor : '#fff' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
          <View style={style.editContent}>
            <TextInput
              style={[style.inputText, style.editInputText]}
              blurOnSubmit={false}
              placeholder="Enter your quick text, no contact information or advertisements"
              underlineColorAndroid="transparent"
              multiline
              maxLength={400}
              ref={(ref) => {
                this.inputText = ref;
              }}
              onChangeText={(text) => {
                this.setState({
                  count: text.length,
                  msgText: text,
                });
              }}
              defaultValue={msgText}
              textAlignVertical="top"
            />
            <Text style={style.editTextCount}>
              <Text style={style.numberCount}>{this.state.count}</Text>
              /400
            </Text>
          </View>
        </ScrollView>

        <LoadingModal isOpen={this.showLoading} loadingTips={false} />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
