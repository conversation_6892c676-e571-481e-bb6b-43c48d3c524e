import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { Icon } from 'react-native-elements';
import I18n, { setLanguage } from '../i18n';
import I18nUtil from '../util/I18nUtil';
import res from '../res';
import Image from '../components/image';
import TooltipMenu from '../components/tooltipMenu';
import { statusBarHeight, deviceWidth, deviceHeight, footerHeight, hasEar } from '../common';
import { titleColor, subTitleColor, primaryColor } from '../themes';
import Storage from '../common/storage';

// 国家列表数据 - 根据图片显示的国家
const countryList = [
  {
    id: 1,
    name: '柬埔寨',
    nameEn: 'Cambodia',
    nameKm: 'កម្ពុជា',
    nameVi: 'Campuchia',
    nameTh: 'กัมพูชา',
    nameKo: '캄보디아',
    flag: res.khFlag,
    code: '855',
  },
  {
    id: 2,
    name: '菲律宾',
    nameEn: 'Philippines',
    nameKm: 'ហ្វីលីពីន',
    nameVi: 'Philippines',
    nameTh: 'ฟิลิปปินส์',
    nameKo: '필리핀',
    flag: res.phFlag,
    code: '63',
  },
  {
    id: 3,
    name: '新加坡',
    nameEn: 'Singapore',
    nameKm: 'សិង្ហបុរី',
    nameVi: 'Singapore',
    nameTh: 'สิงคโปร์',
    nameKo: '싱가포르',
    flag: res.sgFlag,
    code: '65',
  },
  {
    id: 4,
    name: '马来西亚',
    nameEn: 'Malaysia',
    nameKm: 'ម៉ាឡេស៊ី',
    nameVi: 'Malaysia',
    nameTh: 'มาเลเซีย',
    nameKo: '말레이시아',
    flag: res.myFlag,
    code: '60',
  },
  {
    id: 5,
    name: '印度尼西亚',
    nameEn: 'Indonesia',
    nameKm: 'ឥណ្ឌូនេស៊ី',
    nameVi: 'Indonesia',
    nameTh: 'อินโดนีเซีย',
    nameKo: '인도네시아',
    flag: res.mcFlag,
    code: '62',
  },
  {
    id: 6,
    name: '中国香港',
    nameEn: 'Hong Kong',
    nameKm: 'ហុងកុង',
    nameVi: 'Hồng Kông',
    nameTh: 'ฮ่องกง',
    nameKo: '홍콩',
    flag: res.hkFlag,
    code: '852',
  },
];

export default class CountrySelection extends Component {
  static navigationOptions = {
    headerShown: false,
    gestureEnabled: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedCountry: null,
    };
  }

  componentDidMount() {
    // 设置状态栏样式
    StatusBar.setBarStyle('dark-content', true);
  }

  // 切换语言
  onSwitchLanguage = (language) => {
    setLanguage(language);
    if (global.IS_IOS) {
      I18nUtil.modifyDefaultLanguage(language, () => {});
    }
    global.emitter.emit('languageChange', true);
    this.forceUpdate();
  };

  // 获取国家名称（根据当前语言）
  getCountryName = (country) => {
    const locale = I18n.locale;
    switch (locale) {
      case 'zh':
        return country.name;
      case 'en':
        return country.nameEn;
      case 'km':
        return country.nameKm;
      case 'vi':
        return country.nameVi;
      case 'th':
        return country.nameTh;
      case 'ko':
        return country.nameKo;
      default:
        return country.nameEn;
    }
  };

  // 选择国家
  onSelectCountry = (country) => {
    this.setState({ selectedCountry: country });
  };

  // 完成选择
  onComplete = async () => {
    const { selectedCountry } = this.state;
    if (selectedCountry) {
      try {
        // 保存选择的国家到本地存储
        // 这里可以根据需要保存国家信息

        // 标记不再是首次安装
        await Storage.setFirstInstall(false);

        // 跳转到登录页面
        this.props.navigation.navigate('login');
      } catch (error) {
        console.error('保存国家选择失败:', error);
        // 即使保存失败也跳转到登录页面
        this.props.navigation.navigate('login');
      }
    }
  };

  // 渲染国家项
  renderCountryItem = ({ item, index }) => {
    const { selectedCountry } = this.state;
    const isSelected = selectedCountry && selectedCountry.id === item.id;
    const isLeftColumn = index % 2 === 0;

    return (
      <TouchableOpacity
        style={[
          styles.countryItem,
          isLeftColumn ? styles.leftColumn : styles.rightColumn,
          isSelected && styles.selectedCountryItem,
        ]}
        onPress={() => this.onSelectCountry(item)}
      >
        <Image source={item.flag} style={styles.countryFlag} />
        <Text style={[styles.countryName, isSelected && styles.selectedCountryName]}>
          {this.getCountryName(item)}
        </Text>
      </TouchableOpacity>
    );
  };

  render() {
    const { selectedCountry } = this.state;

    return (
      <View style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />

        <View style={{ backgroundColor: '#f5f5f5', flex: 1 }}>
          {/* 头部 */}
          <View style={styles.header}>
            <Text style={styles.title}>{I18n.t('page_country_selection_title')}</Text>

            {/* 语言切换 */}
            <View style={styles.languageSection}>
              <TooltipMenu
                componentWrapperStyle={styles.languageTooltipWrap}
                buttonComponent={
                  <View style={styles.languageWrap}>
                    <Text style={styles.languageText}>{I18n.t('language')}</Text>
                    <Icon type="antdesign" name="down" size={14} color="#666" />
                  </View>
                }
                items={[
                  {
                    label: I18n.t('language', { locale: 'zh' }),
                    icon: res.cnFlag,
                    onPress: () => this.onSwitchLanguage('zh'),
                  },
                  {
                    label: I18n.t('language', { locale: 'en' }),
                    icon: res.usFlag,
                    onPress: () => this.onSwitchLanguage('en'),
                  },
                  {
                    label: I18n.t('language', { locale: 'km' }),
                    icon: res.khFlag,
                    onPress: () => this.onSwitchLanguage('km'),
                  },
                  {
                    label: I18n.t('language', { locale: 'vi' }),
                    icon: res.vnFlag,
                    onPress: () => this.onSwitchLanguage('vi'),
                  },
                  {
                    label: I18n.t('language', { locale: 'th' }),
                    icon: res.thFlag,
                    onPress: () => this.onSwitchLanguage('th'),
                  },
                  {
                    label: I18n.t('language', { locale: 'ko' }),
                    icon: res.krFlag,
                    onPress: () => this.onSwitchLanguage('ko'),
                  },
                ]}
              />
            </View>
          </View>

          {/* 国家列表 */}
          <View style={styles.content}>
            <FlatList
              data={countryList}
              renderItem={this.renderCountryItem}
              keyExtractor={(item) => item.id.toString()}
              numColumns={2}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContainer}
            />
          </View>

          {/* 底部完成按钮 */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.completeButton, selectedCountry && styles.completeButtonActive]}
              onPress={this.onComplete}
              disabled={!selectedCountry}
            >
              <Text
                style={[
                  styles.completeButtonText,
                  selectedCountry && styles.completeButtonTextActive,
                ]}
              >
                {I18n.t('page_country_selection_complete')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    marginTop: statusBarHeight + 10,
    paddingBottom: hasEar ? 24 : 15,
  },
  statusBar: {
    backgroundColor: '#ff4757',
    paddingVertical: 8,
    alignItems: 'center',
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  header: {
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: titleColor,
  },
  languageSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageTooltipWrap: {
    // 样式由TooltipMenu组件处理
  },
  languageWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4a90e2',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  languageText: {
    color: '#fff',
    fontSize: 14,
    marginRight: 5,
  },
  content: {
    flex: 1,
  },
  listContainer: {
    padding: 20,
  },
  countryItem: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#f0f0f0',
    minHeight: 80,
  },
  leftColumn: {
    marginRight: 7.5,
  },
  rightColumn: {
    marginLeft: 7.5,
  },
  selectedCountryItem: {
    borderColor: '#4a90e2',
    backgroundColor: '#f0f8ff',
  },
  countryFlag: {
    width: 30,
    height: 20,
    marginBottom: 8,
    resizeMode: 'cover',
  },
  countryName: {
    fontSize: 14,
    color: titleColor,
    textAlign: 'center',
    fontWeight: '500',
  },
  selectedCountryName: {
    color: '#4a90e2',
    fontWeight: '600',
  },
  footer: {
    backgroundColor: '#fff',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  completeButton: {
    backgroundColor: '#ccc',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  completeButtonActive: {
    backgroundColor: '#4a90e2',
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#999',
  },
  completeButtonTextActive: {
    color: '#fff',
  },
});
