# 国家选择页面功能实现

## 功能概述

根据您提供的UI设计图，我已经完成了国家选择页面的实现。这个页面在用户首次安装应用时显示，用户需要选择国家后才能进入注册登录页面。

## 实现的功能

### 1. 页面结构
- ✅ 顶部红色状态栏显示"默认跟随系统语言"
- ✅ 页面标题"请选择国家/地区"
- ✅ 右上角语言切换下拉菜单
- ✅ 国家列表（2列布局）
- ✅ 底部"完成"按钮

### 2. 语言切换功能
- ✅ 支持6种语言：中文、英文、高棉语、越南语、泰语、韩语
- ✅ 参考登录页面的语言切换实现
- ✅ 使用TooltipMenu组件实现下拉菜单
- ✅ 切换语言后页面内容实时更新

### 3. 国家选择功能
- ✅ 显示6个国家：柬埔寨、菲律宾、新加坡、马来西亚、印度尼西亚、中国香港
- ✅ 使用countryCode组件的国旗图标
- ✅ 根据当前语言显示对应的国家名称
- ✅ 选中状态的视觉反馈（蓝色边框和背景）
- ✅ 2列网格布局

### 4. 首次安装检测
- ✅ 添加首次安装标记存储
- ✅ 修改启动页面逻辑，首次安装时跳转到国家选择页面
- ✅ 选择完成后标记不再是首次安装

## 修改的文件

### 1. 新增文件
- `src/pages/countrySelection.js` - 国家选择页面主文件

### 2. 修改的文件
- `src/pages/index.js` - 添加路由配置
- `src/pages/first.js` - 添加首次安装检测逻辑
- `src/common/storage.js` - 添加首次安装标记存储方法
- `src/i18n/locales/*.json` - 添加多语言文本

### 3. 添加的国际化文本
```json
{
  "page_country_selection_title": "请选择国家/地区",
  "page_country_selection_system_language": "默认跟随系统语言", 
  "page_country_selection_complete": "完成"
}
```

## 页面流程

1. **应用启动** → `src/pages/first.js`
2. **检测首次安装** → 如果是首次安装，跳转到国家选择页面
3. **国家选择页面** → `src/pages/countrySelection.js`
4. **选择国家** → 用户点击选择国家
5. **点击完成** → 标记不再是首次安装，跳转到登录页面
6. **后续启动** → 直接跳转到主页面，不再显示国家选择页面

## 技术实现细节

### 1. 国家数据结构
```javascript
{
  id: 1,
  name: '柬埔寨',           // 中文名
  nameEn: 'Cambodia',      // 英文名
  nameKm: 'កម្ពុជា',        // 高棉语名
  nameVi: 'Campuchia',     // 越南语名
  nameTh: 'กัมพูชา',        // 泰语名
  nameKo: '캄보디아',        // 韩语名
  flag: res.khFlag,        // 国旗图标
  code: '855',             // 国家代码
}
```

### 2. 语言切换实现
- 使用`setLanguage()`函数切换语言
- 调用`I18nUtil.modifyDefaultLanguage()`处理iOS平台
- 发送`languageChange`事件通知其他组件
- 调用`forceUpdate()`刷新页面

### 3. 首次安装检测
- 使用AsyncStorage存储首次安装标记
- 默认值为`true`（首次安装）
- 完成国家选择后设置为`false`

## 样式设计

### 1. 颜色方案
- 主色调：`#4a90e2`（蓝色）
- 选中状态：蓝色边框 + 浅蓝背景
- 状态栏：`#ff4757`（红色）

### 2. 布局设计
- 使用SafeAreaView确保在刘海屏上正常显示
- FlatList实现2列网格布局
- 响应式设计，适配不同屏幕尺寸

## 使用方法

1. 确保所有依赖已安装
2. 重新启动应用
3. 首次安装时会自动显示国家选择页面
4. 选择国家后点击"完成"按钮
5. 后续启动将直接进入主页面

## 注意事项

1. 页面使用了现有的组件和样式系统
2. 国旗图标来自`src/res/index.js`中的定义
3. 语言切换功能与登录页面保持一致
4. 首次安装标记会在应用数据清除时重置

## 测试建议

1. 测试首次安装流程
2. 测试语言切换功能
3. 测试国家选择和完成流程
4. 测试不同语言下的国家名称显示
5. 测试页面在不同设备上的显示效果
